.zg_footer {
  width: 100%;
  margin-top: -245px;
}

.zg_footer_box {
  padding: 40px 0;
  width: 1000px;
  margin: 0 auto;
}

.zg_footer_box h4 {
  font-size: 40px;
  color: #fff100;
  font-weight: bold;
  text-align: center;
  margin-bottom: 30px;
}

.foot {
  width: 100%;
  min-width: 1000px;
  text-align: center;
  padding: 15px 0 30px;
  font-size: 14px;
  line-height: 30px;
  color: #fff;
  background-color: #333;
}

.foot a {
  color: #fff;
  display: inline-block;
}

/* 省份选择弹框样式 */
.province-modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
}

.province-modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.province-modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #fff;
  border: 4px solid #ff6600;
  border-radius: 20px;
  padding: 0;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
  max-width: 1100px;
  width: 90%;
  max-height: 85vh;
  /* overflow: hidden; */
}

.province-modal-header {
  position: absolute;
  top: -100px;
  left: 50%;
  margin-left: -184px;
  transform: scale(0.6);
}

.header-title {
  display: inline-block;
  background: linear-gradient(135deg, #ff9900 0%, #ff6600 100%);
  border: 3px solid #ffaa00;
  border-radius: 30px;
  padding: 12px 25px;
  color: #fff;
  font-size: 20px;
  font-weight: bold;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  box-shadow: 0 4px 12px rgba(255, 102, 0, 0.3);
}

.title-text {
  margin-right: 5px;
}

.title-heart {
  color: #fff;
}

.province-modal-close {
  position: absolute;
  top: -50px;
  right: 20px;
  width: 38px;
  height: 38px;
  background: url(../images/close-btn.png) no-repeat bottom;
}

.province-modal-close:hover {
  color: #fff;
  transform: scale(1.1);
}

.province-modal-body {
  border-radius: 0 0 16px 16px;
}

.province-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 15px;
  max-width: 100%;
  background: #c41e3a;
  padding: 20px;
  border-radius: 12px;
}

.province-item {
  text-align: center;
  padding: 8px 4px;
  border-radius: 8px;
  transition: all 0.3s ease;
  background: #fff;
  border: 1px solid #ddd;
}

.province-item:hover {
  background-color: #f0f0f0;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.qr-code {
  width: 90px;
  height: 90px;
  margin: 0 auto 8px;
  border: 1px solid #ccc;
  border-radius: 6px;
  overflow: hidden;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qr-code img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.province-name {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  margin-top: 6px;
  line-height: 1.2;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .province-modal-content {
    width: 95%;
    max-height: 90vh;
  }

  .province-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
    padding: 15px;
  }

  .qr-code {
    width: 60px;
    height: 60px;
  }

  .province-name {
    font-size: 11px;
  }

  .header-title {
    font-size: 16px;
    padding: 8px 18px;
  }

  .province-modal-close {
    width: 35px;
    height: 35px;
    font-size: 28px;
  }
}

/*页面开始*/

.zg_w1200 {
  position: relative;
  width: 1200px;
  margin: 0 auto;
}
.zg_w1000 {
  position: relative;
  width: 1000px;
  margin: 0 auto;
}

/*头部*/
.zg_head {
  position: relative;
  z-index: 1;
  margin: 0 auto;
  width: 100%;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  overflow: hidden;
  background: #ab0005;
}

.zg_headdiv {
  float: left;
  display: flex;
  align-items: center;
}

.zg_logo {
  display: block;
  width: 140px;
  height: 50px;
  overflow: hidden;
}

.zg_logo img {
  width: 100%;
  padding-top: 8px;
}

.zg_head_nav {
  flex: 1;
  margin-top: 12px;
  margin-left: 50px;
}

.zg_head_nav a {
  font-size: 14px;
  color: #fff;
  cursor: pointer;
}

.zg_head_nav span {
  font-size: 12px;
  color: #fff;
  padding: 0 10px;
}

.zg_head_right {
  float: right;
  margin-top: 19px;
}

.zg_head_right a {
  font-size: 16px;
  color: #fff;
  cursor: pointer;
}

.zg_head_nav a:hover {
  color: #e60012;
}

/*头部结束*/
/*banner*/

.zg_banner {
  position: relative;
  width: 100%;
  height: 1015px;
  background: url(../images/banner.png) no-repeat center top;
  overflow: hidden;
}
.zg_ban_h {
  height: 1190px;
}

.banner-title {
  position: absolute;
  top: 190px;
  left: 115px;
}

.banner-subtitle {
  position: absolute;
  top: 250px;
  right: 85px;
  z-index: 10;
}

.banner-top {
  position: absolute;
  top: 60px;
  left: 100px;
}

.banner {
  box-sizing: border-box;
  padding-top: 550px;
}

.banner-bottom1 {
  /* width: 640px; */
  height: 80px;
  background: linear-gradient(to right, transparent 0%, #d1111b 40%, #d1111b 100%);
  line-height: 80px;
  color: #fff;
  text-align: right;
  box-sizing: border-box;
  font-size: 38px;
  border-radius: 40px;
  padding-right: 40px;
  transform: rotate(-4deg) !important;
  margin-right: 540px;
}

.banner-bottom2 {
  height: 80px;
  background: linear-gradient(to left, transparent 0%, #3d3180 40%, #3d3180 100%);
  line-height: 80px;
  color: #fff;
  box-sizing: border-box;
  font-size: 38px;
  border-radius: 40px;
  padding-left: 40px;
  transform: rotate(-4deg) !important;
  margin-left: 400px;
  margin-top: -34px;
}

/*banner结束*/
.zg_wrap {
  position: relative;
  width: 100%;
  background:url(../images/bg.png) no-repeat top;
  background-size: 1920px;
  margin-top: -40px;
}

.zg_tittle {
  width: 100%;
  text-align: center;
}
.part-btn {
  animation: 1.5s pulse infinite;
}

.part-select {
  position: relative;
  width: 415px;
  height: 127px;
  cursor: pointer;
  margin-top: 40px;
  margin-left: 15px;
  margin-right: 15px;
}

/* part1 */
.nav {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.nav-item {
  width: 30%;
  text-align: center;
  border: 1px dashed #e70207;
  border-radius: 20px;
  padding: 20px 30px;
  box-sizing: border-box;
}

.nav-item:nth-child(1) {
  background: linear-gradient(to right, #800820 0%, #7c0821 50%, #800820 100%);
  margin-top: 100px;
}

.nav-item:nth-child(2) {
  background: linear-gradient(to right, #5c0d30 0%, #5b0d30 50%, #5c0d30 100%);
  margin-top: 50px;
}

.nav-item:nth-child(3) {
  background: linear-gradient(to right, #3c123f 0%, #3b123f 50%, #3c123f 100%);
}

.nav-item-subtitle {
  font-size: 20px;
  font-weight: bold;
  background: linear-gradient(to top, #ffe22f 0%, #fff 50%, #ffecb3 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

.nav-item-title {
  font-size: 30px;
  font-weight: bold;
  background: linear-gradient(to top, #ffe22f 0%, #fff 50%, #ffecb3 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  margin-top: 5px;
}

.nav-item-line {
  border-bottom: 1px dashed #e70207;
  margin-top: 15px;
  margin-bottom: 20px;
}

.nav-item-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-item-sub {
  width: 85px;
  height: 85px;
  box-sizing: border-box;
  border: 3px solid #ff0000;
  border-radius: 20px;
  background-color: rgb(255, 255, 255);
  box-shadow: inset 0 4px 16px #ff8a7a;
  font-size: 50px;
  color: #d0020a;
  line-height: 1;
  padding-top: 12px;
  font-weight: bold;
  cursor: pointer;
}

.nav-item-sub.active {
  background-color: #ff0000;
  color: #fff;
  box-shadow: none;
}

.nav-line {
  margin-left: -430px;
  margin-top: -85px;
}

.nav-title {
  font-size: 52px;
  font-weight: bold;
  line-height: 1;
  color: #ff0000;
}

.nav-title span {
  font-size: 70px;
  font-family: Impact, Haettenschweiler, 'Arial Narrow Bold', sans-serif;
}

.part1 {
  margin-top: 260px;
}

.part-card {
  position: relative;
  border-top: none;
  background: url(../images/part-card-center.png) repeat-y;
}

.part-card:before {
  content: '';
  position: absolute;
  top: -80px;
  left: 0;
  background: url(../images/part-card-top.png) no-repeat top;
  height: 80px;
  width: 100%;
  background-size: 100% 100%;
}
.part-card::after {
  content: '';
  position: absolute;
  bottom: -80px;
  left: 0;
  background: url(../images/part-card-bottom.png) no-repeat top;
  height: 80px;
  width: 100%;
  background-size: 100% 100%;
}

.part-title {
  position: absolute;
  top: -100px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
}

.part-title-sub {
  font-size: 38px;
  font-weight: bold;
  background: linear-gradient(-135deg, #ffe22f 0%, #ffecb3 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  border-top: 1px dashed #c01c17;
  border-bottom: 1px dashed #c01c17;
  margin-top: 10px;
  line-height: 1.5;
  padding-bottom: 3px;
  min-width: 550px;
}

.part-title {
  margin-top: -160px;
}

/* fixed */

.fixed{
  position: fixed;
  top: 10%;
  left: 0px;
  z-index: 999;
  width: 263px;
  height: 580px;
  background: url(../images/fixed-bg.png) no-repeat center;
  transform: scale(0.7);
  padding-left: 30px;
  box-sizing: border-box;
  box-sizing: border-box;
  padding-top: 187px;
}

.fixed-close {
  position: absolute;
  width: 38px;
  height: 38px;
  background: url(../images/close-btn.png) no-repeat top;
  top: 0px;
  right: 20px;
  cursor: pointer;
}

.fixed-item {
  width: 200px;
  height: 50px;
  cursor: pointer;
  margin-bottom: 15px;
  text-align: center;
  box-sizing: border-box;
  /* background-color: rgba(0, 0, 0, .5); */
}

.part-link {
  cursor: pointer;
}

.part-table {
  display: flex;
  justify-content: center;
  align-items: stretch;
}

.part-table-title {
  position: relative;
  font-size: 24px;
  background-color: #d7000f;
  color: #fff;
  font-weight: bold;
  min-height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.part-table-title2 {
  background-color: #940100;
}

.part-table-title3 {
  padding-top: 120px;
  line-height: 1.4;
}

.part-table-col1 {
  width: 715px;
}

.part-table-col2 {
  width: 324px;
}

.part-table-col3 {
  width: 90px;
}

.part-table-content {
  position: relative;
  font-size: 20px;
  background-color: #fff;
  min-height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.part-table-content span {
  color: #d7000f;
}

.part-table-content:nth-child(2n-1) {
  background-color: #f5f5f5;
}

.part-table-line {
  position: absolute;
  border-radius: 10px;
  padding: 0 14px;
  font-size: 22px;
  background-color: #d7000f;
  height: 38px;
  line-height: 38px;
  bottom: -19px;
  right: -90px;
  z-index: 9;
  box-sizing: border-box;
  color: #fff;
}

.part-table-line::before {
  content: '';
  position: absolute;
  top: 19px;
  right: -235px;
  width: 235px;
  height: 1px;
  background-color: #d7000f;
}

.part-table-line::after {
  content: '';
  position: absolute;
  top: 19px;
  left: -590px;
  width: 590px;
  height: 1px;
  background-color: #d7000f;
}

.part-table-pirce {
  position: absolute;
  width: 163px;
  height: 148px;
  right: -5px;
  top: 80px;
}

.bounceY {
  animation: bounceY 1.5s infinite alternate linear;
}

@keyframes bounceY {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
  100% {
    transform: translateY(0);
  }
}

.part-book {
  border-radius: 40px;
  width: 1123px;
  margin: 125px auto 0;
  position: relative;
  height: 710px;
  background: url(../images/part-book.png) no-repeat -18px 80px #fff;
  box-sizing: border-box;
}

.part-book-title {
  background-image: linear-gradient( to left, rgb(255,241,0) 0%, rgb(255,252,219) 100%);
  box-shadow: 0px 7px 18px rgba(142, 0, 0, 0.9);
  line-height: 76px;
  text-align: center;
  font-size: 34px;
  font-weight: bold;
  color: #f60000;
  border-radius: 0 0 50px 0;
  margin-top: -35px;
  padding-left: 50px;
  padding-right: 50px;
}

.part-book-content {
  padding-left: 600px;
}

.part-book-content-title {
  position: relative;
  font-size: 32px;
  font-weight: bold;
  color: #333333;
  margin-top: 25px;
}

.part-book-content-title span {
  color: #f60000;
}

.part-book-content-subtitle {
  font-size: 28px;
  margin-top: 10px;
  color: #333333;
}

.part-book-content-title::before {
  position: absolute;
  content: '';
  top: 50%;
  margin-top: -6.5px;
  left: -24px;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 7.5px 0 7.5px 13px;
  border-color: transparent transparent transparent #f60000;
}

.part-book-content-info {
  font-size: 24px;
  color: #fff;
  margin-top: 100px;
  line-height: 1.5;
}

.part-book-content-desc {
  font-size: 30px;
  font-weight: bold;;
  color: #fff;
  margin-top: 30px;
  line-height: 1.5;
}

.part-book-content-desc span {
  font-style: italic;
  background: linear-gradient(-45deg, #ffe22f 0%, #fff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

.part-book-content-btn {
  margin-top: 40px;
}

/* part2 */

.part2 {
  margin-top: 400px;
}

.part2-box {
  border: 3px solid #f60000;
  margin-left: 18px;
  margin-right: 18px;
  border-radius: 20px;
  width: 430px;
}

.part2-box-title {
  font-size: 30px;
  background-color: #f60000;
  text-align: center;
  color: #fff;
  border-radius: 10px 10px 0 0;
  line-height: 60px;
}

.part2-box-subtitle {
  background-color: #fff;
  height: 120px;
  border-radius: 0 0 18px 18px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 30px;
  flex-direction: column;
}

.part2-box-subtitle span {
  font-size: 22px;
  margin-top: 10px;
}

.part-paper {
  width: 1129px;
  height: 493px;
  margin: 74px auto 0;
  position: relative;
  background: url(../images/part-paper.png) no-repeat top;
  background-size: 100% 100%;
  box-sizing: border-box;
  padding-left: 490px;
  padding-top: 180px;
}

.part2-paper-title {
  font-size: 26px;
  background: linear-gradient(-45deg, #ffe22f 0%, #ffecb3 50%, #fff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  margin-bottom: 15px;
  font-weight: bold;
  width: 180px;
}

.part2-paper-subtitle {
  font-size: 26px;
  color: #fff;
  font-weight: normal;
  margin-left: 17px;
}

.part-paper-btn {
  position: absolute;
  bottom: -65px;
  right: 225px;
}

.part-class {
  width: 1130px;
  height: 410px;
  background: url(../images/part-class.png) no-repeat top;
  background-size: 100% 100%;
  margin: 140px auto 0;
}

.part-class-content {
  margin-left: 580px;
  margin-top: -15px;
}

.part-class-content-title {
  font-size: 30px;
}

.part-class-content-title span {
  font-weight: bold;
  color: #f60000;
}

.part-class-content-subtitle {
  margin-top: 50px;
  color: #fff;
  font-size: 24px;
  line-height: 1.8;
}

.part-class-content-btn {
  margin-top: 30px;
}

.part-book-link {
  position: absolute;
  top: 20px;
  right: -15px;
}

.part3 {
  margin-top: 500px;
}

.part-title-sub span {
  font-size: 50px;
  font-family: Impact, Haettenschweiler, 'Arial Narrow Bold', sans-serif;
  line-height: 1;
  margin-left: 10px;
  margin-right: 10px;
  font-style: italic;
}

.part3 .part-title {
  top: -160px;
}

.part-teach {
  position: relative;
  width: 1087px;
  height: 1404px;
  background: url(../images/part-teach.png) no-repeat top;
  background-size: 100% 100%;
  margin: 0 auto 0;
}

.part-teach-btn {
  position: absolute;
  bottom: 30px;
  left: 50%;
  margin-left: -180px;
}

.part-teach::before {
  position: absolute;
  content: '';
  width: 160px;
  height: 184px;
  background: url(../images/box1.png) no-repeat center;
  left: -50px;
  top: -50px;
}

.part-teach::after {
  position: absolute;
  content: '';
  width: 201px;
  height: 232px;
  background: url(../images/box2.png) no-repeat center;
  right: -100px;
  top: 590px;
}

.part3::before {
  position: absolute;
  content: '';
  width: 450px;
  height: 520px;
  background: url(../images/box3.png) no-repeat center;
  left: -320px;
  bottom: -100px;
  z-index: 1;
}

.part2-subtitle {
  font-size: 24px;
  color: #fff;
  margin-top: -30px;
  text-align: center;
}

.part-wr {
  position: relative;
  width: 1129px;
  height: 560px;
  background: url(../images/part-wr.png) no-repeat top;
  margin: 70px auto 0;
}

.part-wr1 {
  padding-top: 140px;
  width: 358px;
  text-align: right;
}

.part-wr2 {
  padding-top: 140px;
  width: 380px;
  text-align: left;
}

.part-wr-title {
  font-size: 24px;
  color: #f60000;
  font-weight: bold;
}

.part-wr-subtitle {
  font-size: 22px;
}

.part-wr-top {
  margin-bottom: 170px;
}

.part-mk {
  width: 1129px;
  height: 345px;
  background: url(../images/part-mk.png) no-repeat top;
  margin: 50px auto -30px;
}

.part-mk-left {
  width: 195px;
  margin-left: 110px;
  margin-top: 130px;
}

.part-mk-qr-title {
  font-size: 16px;
  color: #c7000b;
  margin-top: 14px;  
}

.part-mk-right {
  width: 715px;
  height: 258px;
  margin-right: 70px;
  margin-top: 44px;
  padding-top: 25px;
}

.part-mk-title {
  font-size: 20px;
  font-weight: bold;
  background: #fff;
  border-radius: 10px;
  padding: 5px 10px;
  color: #c7000b;
  margin-right: 20px;
}

.part-mk-subtitle {
  width: 555px;
  font-size: 20px;
  color: #fff;
  line-height: 1.5;
}

.part-mk-item {
  margin-bottom: 16px;
  margin-left: 25px;
}

.part6-content {
  width: 1088px;
  margin: 0 auto;
  background-color: #fff;
  border-radius: 30px;
  padding-bottom: 70px;
}

.part6-award {
  width: 1038px;
  height: 639px;
  background: url(../images/part-award.png);
  margin-top: 40px;
  box-sizing: border-box;
  padding-top: 86px;
  padding-left: 140px;
}

.part6-item {
  line-height: 1;
  margin-bottom: 120px;
  color: #fff;
}

.part6-item-top {
  font-size: 24px;
}

.part6-item-title {
  font-size: 34px;
  font-weight: bold;
  line-height: 1.1;
  margin-top: 3px;
  margin-bottom: 3px;
}

.part6-item-subtitle {
  font-size: 24px;
  font-weight: bold;
}

.part6-bottom {
  background: linear-gradient(to right, #f60000 0%, #f83333 50%, #fedfdf 100%);
  margin-top: 60px;
}

.part6-list {
  padding-top: 25px;
  padding-bottom: 30px;
  margin-left: 25px;
  margin-right: 25px;
}

.part6-list-num {
  font-size: 23px;
  color: #fff;
  width: 158px;
  border-bottom: 1px dashed #fff;
  padding-bottom: 10px;
  margin: 0 auto;
}

.part6-list-title {
  font-size: 28px;
  font-weight: bold;
  color: #fff;
  line-height: 1.3;
  margin-top: 15px;
  margin-bottom: 15px;
}

.part6-list-price {
  font-size: 22px;
  color: #fff;
  font-weight: bold;
  margin-top: 18px;
}

.part6 {
  margin-top: 400px;
  position: relative;
}

.part6::before {
  position: absolute;
  content: '';
  width: 264px;
  height: 304px;
  background: url(../images/box4.png) no-repeat center;
  right: -100px;
  bottom: 300px;
  z-index: 1;
}

.part6::after {
  position: absolute;
  content: '';
  width: 450px;
  height: 420px;
  background: url(../images/box5.png) no-repeat center;
  left: -200px;
  bottom: -200px;
  z-index: 1;
}

.part-wr-link {
  position: absolute;
  top: -72px;
  right: 160px;
}

.part-room::before {
  position: absolute;
  content: '';
  width: 160px;
  height: 184px;
  background: url(../images/box1.png) no-repeat center;
  left: -50px;
  top: -50px;
}

.part-room::after {
  position: absolute;
  content: '';
  width: 201px;
  height: 232px;
  background: url(../images/box2.png) no-repeat center;
  right: -100px;
  top: 590px;
}

.part8 {
  margin-top: 400px;
}

.part-room {
  width: 1130px;
  margin: 0 auto;
  background-color: #fff;
  border-radius: 30px;
  padding-top: 66px;
  padding-bottom: 100px;
}

.part-room-pic2 {
  margin-top: 30px;
}

.part-room-btn {
  position: absolute;
  left: 50%;
  margin-left: -165px;
  bottom: -60px;
  cursor: pointer;
}

.part9 {
  margin-top: 500px;
}
